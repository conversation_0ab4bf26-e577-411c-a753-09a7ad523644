<template>
  <view class="search-page">
    <NavBar title="搜索" />

    <!-- 搜索输入框 -->
    <view class="search-input-container">
      <view class="search-input-wrapper">
        <view class="search-icon">🔍</view>
        <input class="search-input" type="text" placeholder="搜索冥想课程..." v-model="searchKeyword" @confirm="onSearch"
          focus />
        <view class="search-btn" @click="onSearch">搜索</view>
      </view>
    </view>

    <!-- 搜索结果 -->
    <view v-if="hasSearched" class="search-results">
      <!-- 分类标签 -->
      <scroll-view class="category-tabs" scroll-x="true">
        <view class="tabs">
          <view v-for="(category, index) in categories" :key="category.id" class="tab-item"
            :class="{ active: activeCategoryIndex === index }" @click="onCategoryClick(index)">
            {{ category.name }}
          </view>
        </view>
      </scroll-view>

      <!-- 结果列表 -->
      <scroll-view class="results-content" scroll-y="true">
        <view v-if="currentResults.length > 0" class="results-grid">
          <ContentCard v-for="item in currentResults" :key="item.id" :id="item.id" :title="item.title"
            :cover="item.cover" :learners="item.learners" :category="item.category" @click="onItemClick" />
        </view>
        <view v-else class="no-results">
          <text class="no-results-text">暂无搜索结果</text>
        </view>
      </scroll-view>
    </view>

    <!-- 搜索建议 -->
    <view v-else class="search-suggestions">
      <view class="suggestions-title">热门搜索</view>
      <view class="suggestions-list">
        <view v-for="suggestion in hotSearches" :key="suggestion" class="suggestion-item"
          @click="onSuggestionClick(suggestion)">
          {{ suggestion }}
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import NavBar from '@/components/NavBar.vue'
import ContentCard from '@/components/ContentCard.vue'
import { MeditationApi, type MeditationContent, type MeditationTag } from '@/api/meditation'

// 搜索相关
const searchKeyword = ref('')
const hasSearched = ref(false)
const activeCategoryIndex = ref(0)
const loading = ref(false)

// 分类数据
const categories = ref([
  { id: 'all', name: '全部' },
  { id: 'meditation', name: '冥想' },
  { id: 'sleep', name: '睡眠' },
  { id: 'sound', name: '声音' }
])

// 热门搜索（可以从标签API获取）
const hotSearches = ref<string[]>([])

// 搜索结果数据
const searchResults = ref<{
  all: MeditationContent[]
  meditation: MeditationContent[]
  sleep: MeditationContent[]
  sound: MeditationContent[]
}>({
  all: [],
  meditation: [],
  sleep: [],
  sound: []
})

// 转换API数据为组件需要的格式
const transformSearchResult = (content: MeditationContent) => ({
  id: content.id.toString(),
  title: content.title,
  cover: content.cover_url || 'https://picsum.photos/240/160?random=' + content.id,
  learners: content.favorite_count || 0,
  category: content.type === 'meditation' ? '冥想' : content.type === 'sleep' ? '睡眠' : '声音'
})

// 获取类型对应的中文名称
const getTypeName = (type: string): 'meditation' | 'sleep' | 'sound' | undefined => {
  const typeMap: { [key: string]: 'meditation' | 'sleep' | 'sound' } = {
    '冥想': 'meditation',
    '睡眠': 'sleep',
    '声音': 'sound'
  }
  return typeMap[type]
}

// 加载热门搜索标签
const loadHotSearches = async () => {
  try {
    const response = await MeditationApi.getTags()
    if (response.data) {
      // 取前6个标签作为热门搜索
      hotSearches.value = response.data.slice(0, 6).map(tag => tag.name)
    }
  } catch (error) {
    console.error('加载热门搜索失败:', error)
    // 使用默认热门搜索
    hotSearches.value = ['正念冥想', '深度睡眠', '压力释放', '专注力', '情绪管理', '白噪音']
  }
}

const currentResults = computed(() => {
  const currentCategory = categories.value[activeCategoryIndex.value]
  const results = searchResults.value[currentCategory.id as keyof typeof searchResults.value] || []
  return results.map(transformSearchResult)
})

// 执行搜索
const performSearch = async (keyword: string) => {
  if (!keyword.trim()) return

  try {
    loading.value = true

    // 并行搜索不同类型的内容
    const [allRes, meditationRes, sleepRes, soundRes] = await Promise.all([
      MeditationApi.search({ keyword, pageSize: 20 }),
      MeditationApi.search({ keyword, type: 'meditation', pageSize: 20 }),
      MeditationApi.search({ keyword, type: 'sleep', pageSize: 20 }),
      MeditationApi.search({ keyword, type: 'sound', pageSize: 20 })
    ])

    // 更新搜索结果
    searchResults.value = {
      all: allRes.data?.list || [],
      meditation: meditationRes.data?.list || [],
      sleep: sleepRes.data?.list || [],
      sound: soundRes.data?.list || []
    }

    hasSearched.value = true
  } catch (error) {
    console.error('搜索失败:', error)
    uni.showToast({
      title: '搜索失败，请重试',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 事件处理
const onSearch = () => {
  if (searchKeyword.value.trim()) {
    performSearch(searchKeyword.value.trim())
  }
}

const onCategoryClick = (index: number) => {
  activeCategoryIndex.value = index
}

const onSuggestionClick = (suggestion: string) => {
  searchKeyword.value = suggestion
  onSearch()
}

const onItemClick = (id: string) => {
  uni.navigateTo({
    url: `/pages/meditation-detail/meditation-detail?id=${id}`
  })
}

// 页面加载时获取热门搜索
onMounted(() => {
  loadHotSearches()
})
</script>

<style scoped>
.search-page {
  padding-top: 176rpx;
  min-height: 100vh;
  background-color: #F7FAFC;
}

.search-input-container {
  padding: 24rpx 32rpx;
  background: transparent;
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  background: #ffffff;
  border-radius: 48rpx;
  padding: 0 32rpx;
  height: 88rpx;
}

.search-icon {
  font-size: 32rpx;
  color: #a0aec0;
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #2d3748;
  background: transparent;
  border: none;
}

.search-btn {
  padding: 16rpx 24rpx;
  background: #7FB069;
  color: #ffffff;
  border-radius: 32rpx;
  font-size: 28rpx;
  margin-left: 16rpx;
}

.search-results {
  flex: 1;
}

.category-tabs {
  /* padding: 24rpx 0; */
}

.tabs {
  display: flex;
  padding: 0 32rpx;
  white-space: nowrap;
}

.tab-item {
  padding: 16rpx 32rpx;
  margin-right: 24rpx;
  border-radius: 48rpx;
  font-size: 28rpx;
  color: #718096;
  background: #f7fafc;
  white-space: nowrap;
  transition: all 0.3s;
}

.tab-item.active {
  background: #7FB069;
  color: #ffffff;
}

.results-content {
  flex: 1;
  padding: 32rpx;
  width: calc(100% - 64rpx);
}

.results-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
  width: 100%;
}

.results-grid :deep(.content-card) {
  width: 100%;
}

.no-results {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.no-results-text {
  font-size: 28rpx;
  color: #a0aec0;
}

.search-suggestions {
  padding: 32rpx;
}

.suggestions-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 24rpx;
}

.suggestions-list {
  display: flex;
  flex-wrap: wrap;
}

.suggestion-item {
  padding: 16rpx 24rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
  background: #ffffff;
  border-radius: 32rpx;
  font-size: 28rpx;
  color: #718096;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
</style>