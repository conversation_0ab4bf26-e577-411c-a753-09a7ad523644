// 冥想相关API接口
import { api, type ApiResponse, buildQueryString } from '@/utils/request'

// 冥想内容接口
export interface MeditationContent {
  id: number
  type: 'meditation' | 'sleep' | 'sound'
  sub_type?: 'course' | 'single'
  parent_id?: number
  title: string
  description?: string
  cover_url?: string
  duration: number
  tags_text?: string
  favorite_count: number
  created_at: string
  updated_at?: string
  is_favorited?: boolean // 当前用户是否收藏
  tags?: MeditationTag[] // 关联的标签
}

// 冥想标签接口
export interface MeditationTag {
  id: number
  name: string
  created_at: string
}

// 获取冥想内容列表的参数
export interface GetMeditationListParams {
  pageNum?: number
  pageSize?: number
  type?: 'meditation' | 'sleep' | 'sound'
}

// 分页响应接口
export interface PaginationResponse<T> {
  list: T[]
  total: number
  pageNum: number
  pageSize: number
  totalPages: number
}

// 搜索冥想内容的参数
export interface SearchMeditationParams {
  keyword: string
  type?: 'meditation' | 'sleep' | 'sound'
  pageNum?: number
  pageSize?: number
}

// 冥想API类
export class MeditationApi {
  // 获取冥想内容列表
  static async getList(params: GetMeditationListParams = {}): Promise<ApiResponse<PaginationResponse<MeditationContent>>> {
    const queryString = buildQueryString({
      pageNum: params.pageNum,
      pageSize: params.pageSize,
      type: params.type
    })

    const url = `/api/meditation/list${queryString ? `?${queryString}` : ''}`

    return api.get<PaginationResponse<MeditationContent>>(url)
  }

  // 获取冥想内容详情
  static async getDetail(id: number): Promise<ApiResponse<MeditationContent>> {
    return api.get<MeditationContent>(`/api/meditation/${id}`)
  }

  // 收藏/取消收藏冥想内容
  static async toggleFavorite(id: number): Promise<ApiResponse<{ is_favorited: boolean }>> {
    return api.post<{ is_favorited: boolean }>(`/api/meditation/${id}/favorite`)
  }

  // 搜索冥想内容
  static async search(params: SearchMeditationParams): Promise<ApiResponse<PaginationResponse<MeditationContent>>> {
    const queryString = buildQueryString({
      keyword: params.keyword,
      type: params.type,
      pageNum: params.pageNum,
      pageSize: params.pageSize
    })

    return api.get<PaginationResponse<MeditationContent>>(`/api/meditation/search?${queryString}`)
  }

  // 获取冥想标签列表
  static async getTags(): Promise<ApiResponse<MeditationTag[]>> {
    return api.get<MeditationTag[]>('/api/meditation/tags')
  }
}
