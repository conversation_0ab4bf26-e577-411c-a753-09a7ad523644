// API请求工具类
import { useUserStore } from '@/stores/user'

// API响应接口
export interface ApiResponse<T = any> {
  code: number
  message: string
  data?: T
  error?: string
}

// 请求配置接口
export interface RequestConfig {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  data?: any
  header?: Record<string, string>
  timeout?: number
  needAuth?: boolean // 是否需要token认证
}

// API基础配置
const API_CONFIG = {
  baseURL: 'http://127.0.0.1:3000',
  timeout: 10000,
  header: {
    'Content-Type': 'application/json'
  }
}

// 请求拦截器 - 添加token
const requestInterceptor = (config: RequestConfig) => {
  const userStore = useUserStore()
  
  // 如果需要认证且有token，则添加到header
  if (config.needAuth !== false && userStore.token) {
    config.header = {
      ...config.header,
      'Authorization': `Bearer ${userStore.token}`
    }
  }
  
  return config
}

// 响应拦截器 - 处理通用错误
const responseInterceptor = <T>(response: any): ApiResponse<T> => {
  const { statusCode, data } = response
  
  // HTTP状态码检查
  if (statusCode !== 200) {
    throw new Error(`HTTP错误: ${statusCode}`)
  }
  
  // 业务状态码检查
  if (data.code !== 200) {
    // token过期或无效
    if (data.code === 401) {
      const userStore = useUserStore()
      userStore.clearUserData()
      uni.showToast({
        title: '登录已过期，请重新登录',
        icon: 'none'
      })
      // 可以在这里跳转到登录页
      // uni.navigateTo({ url: '/pages/login/login' })
    }
    throw new Error(data.message || '请求失败')
  }
  
  return data
}

// 统一请求方法
export const request = <T = any>(config: RequestConfig): Promise<ApiResponse<T>> => {
  return new Promise((resolve, reject) => {
    // 应用请求拦截器
    const finalConfig = requestInterceptor(config)
    
    // 构建完整URL
    const fullUrl = config.url.startsWith('http') 
      ? config.url 
      : `${API_CONFIG.baseURL}${config.url}`
    
    uni.request({
      url: fullUrl,
      method: finalConfig.method || 'GET',
      data: finalConfig.data,
      header: {
        ...API_CONFIG.header,
        ...finalConfig.header
      },
      timeout: finalConfig.timeout || API_CONFIG.timeout,
      success: (response) => {
        try {
          const result = responseInterceptor<T>(response)
          resolve(result)
        } catch (error) {
          reject(error)
        }
      },
      fail: (error) => {
        console.error('请求失败:', error)
        
        // 网络错误处理
        if (error.errMsg?.includes('timeout')) {
          reject(new Error('请求超时，请检查网络连接'))
        } else if (error.errMsg?.includes('fail')) {
          reject(new Error('网络连接失败，请检查网络设置'))
        } else {
          reject(new Error(error.errMsg || '请求失败'))
        }
      }
    })
  })
}

// 构建查询参数字符串的工具函数（兼容小程序环境）
export const buildQueryString = (params: Record<string, string | number | boolean | undefined | null>): string => {
  const queryPairs: string[] = []

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      queryPairs.push(`${encodeURIComponent(key)}=${encodeURIComponent(String(value))}`)
    }
  })

  return queryPairs.join('&')
}

// 便捷方法
export const api = {
  get: <T = any>(url: string, config?: Partial<RequestConfig>) =>
    request<T>({ ...config, url, method: 'GET' }),

  post: <T = any>(url: string, data?: any, config?: Partial<RequestConfig>) =>
    request<T>({ ...config, url, method: 'POST', data }),

  put: <T = any>(url: string, data?: any, config?: Partial<RequestConfig>) =>
    request<T>({ ...config, url, method: 'PUT', data }),

  delete: <T = any>(url: string, config?: Partial<RequestConfig>) =>
    request<T>({ ...config, url, method: 'DELETE' })
}

// 显示加载状态的请求方法
export const requestWithLoading = async <T = any>(
  config: RequestConfig,
  loadingText: string = '加载中...'
): Promise<ApiResponse<T>> => {
  uni.showLoading({ title: loadingText })
  
  try {
    const result = await request<T>(config)
    uni.hideLoading()
    return result
  } catch (error) {
    uni.hideLoading()
    uni.showToast({
      title: error instanceof Error ? error.message : '请求失败',
      icon: 'none'
    })
    throw error
  }
}
