<template>
  <view class="explore-page">
    <NavBar title="探索" :show-back="false" />

    <!-- 搜索框 -->
    <SearchBox />

    <!-- 广告轮播图 -->
    <AdCarousel :ads="adList" @click="onAdClick" />

    <!-- 分类按钮 -->
    <view class="category-buttons">
      <view class="category-btn" @click="onCategoryClick('meditation')">
        <view class="category-icon">🧘</view>
        <text class="category-text">冥想</text>
      </view>
      <view class="category-btn" @click="onCategoryClick('sleep')">
        <view class="category-icon">😴</view>
        <text class="category-text">睡眠</text>
      </view>
      <view class="category-btn" @click="onCategoryClick('sound')">
        <view class="category-icon">🎵</view>
        <text class="category-text">声音</text>
      </view>
    </view>

    <!-- 冥想课程分类 -->
    <scroll-view class="content" scroll-y="true">
      <MeditationSection v-for="section in meditationSections" :key="section.id" :title="section.title"
        :items="section.items" :type="section.type" :view-all-text="section.viewAllText" :is-more-section="section.isMoreSection"
        @view-all="onViewAll" @card-click="onCardClick" />
    </scroll-view>

    <TabBar />
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import NavBar from '@/components/NavBar.vue'
import TabBar from '@/components/TabBar.vue'
import SearchBox from '@/components/SearchBox.vue'
import AdCarousel from '@/components/AdCarousel.vue'
import MeditationSection from '@/components/MeditationSection.vue'
import { MeditationApi, type MeditationContent } from '@/api/meditation'

// 分类点击处理
const onCategoryClick = (category: string) => {
  uni.navigateTo({
    url: `/pages/category/category?type=${category}`
  })
}

// 广告数据
const adList = ref([
  {
    id: '1',
    title: '7天冥想挑战',
    description: '开启你的冥想之旅，每天10分钟',
    image: 'https://picsum.photos/400/200?random=1'
  },
  {
    id: '2',
    title: '深度睡眠冥想',
    description: '让你拥有更好的睡眠质量',
    image: 'https://picsum.photos/400/200?random=2'
  },
  {
    id: '3',
    title: '专注力提升',
    description: '通过冥想提高工作效率',
    image: 'https://picsum.photos/400/200?random=3'
  }
])

// 冥想课程分类数据
const meditationSections = ref<any[]>([])

// 加载状态
const loading = ref(false)

// 格式化时长显示
const formatDuration = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60)
  return `${minutes}分钟`
}

// 转换API数据为组件需要的格式
const transformMeditationData = (content: MeditationContent, index: number) => ({
  id: content.id.toString(),
  title: content.title,
  learners: content.favorite_count || 0,
  index: index + 1 // 从1开始计数
})

// 加载冥想内容数据
const loadMeditationData = async () => {
  try {
    loading.value = true

    // 并行获取不同类型的冥想内容
    const [meditationRes, sleepRes, soundRes] = await Promise.all([
      MeditationApi.getList({ type: 'meditation', pageSize: 4 }),
      MeditationApi.getList({ type: 'sleep', pageSize: 4 }),
      MeditationApi.getList({ type: 'sound', pageSize: 4 })
    ])

    // 获取更多课程（混合类型）
    const moreRes = await MeditationApi.getList({ pageSize: 10 })

    // 构建分类数据
    const sections = []

    console.log('冥想内容', meditationRes, sleepRes, soundRes)
    
    if (meditationRes.data?.items && meditationRes.data.items.length > 0) {
      sections.push({
        id: '1',
        title: '正念冥想',
        type: 'meditation',
        items: meditationRes.data.items.map((item, index) => transformMeditationData(item, index))
      })
    }

    if (sleepRes.data?.items && sleepRes.data.items.length > 0) {
      sections.push({
        id: '2',
        title: '入眠冥想',
        type: 'sleep',
        items: sleepRes.data.items.map((item, index) => transformMeditationData(item, index))
      })
    }

    if (soundRes.data?.items && soundRes.data.items.length > 0) {
      sections.push({
        id: '3',
        title: '声音冥想',
        type: 'sound',
        items: soundRes.data.items.map((item, index) => transformMeditationData(item, index))
      })
    }

    if (moreRes.data?.items && moreRes.data.items.length > 0) {
      sections.push({
        id: '4',
        title: '更多课程',
        viewAllText: '全部课程',
        isMoreSection: true,
        items: moreRes.data.items.map((item, index) => transformMeditationData(item, index))
      })
    }

    meditationSections.value = sections
  } catch (error) {
    console.error('加载冥想数据失败:', error)
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 事件处理

const onAdClick = (item: any) => {
  console.log('点击广告:', item)
  // 这里可以添加广告点击逻辑
}

const onViewAll = (title: string) => {
  console.log('查看全部:', title)

  // 根据分类标题映射到对应的分类类型
  const categoryMap: { [key: string]: string } = {
    '正念冥想': 'meditation',
    '入眠冥想': 'sleep',
    '专注冥想': 'meditation',
    '更多课程': 'meditation'
  }

  const categoryType = categoryMap[title] || 'meditation'

  // 跳转到分类页面
  uni.navigateTo({
    url: `/pages/category/category?type=${categoryType}`
  })
}

const onCardClick = (id: string) => {
  console.log('点击课程:', id)
  // 跳转到冥想详情页面
  uni.navigateTo({
    url: `/pages/meditation-detail/meditation-detail?id=${id}`
  })
}

// 页面加载时获取数据
onMounted(() => {
  loadMeditationData()
})
</script>

<style scoped>
.explore-page {
  padding-top: 176rpx;
  /* 状态栏 + 导航栏高度 */

  min-height: 100vh;
  background-color: #F7FAFC;
  display: flex;
  flex-direction: column;
}

.content {
  flex: 1;
  padding-bottom: 100rpx;
  /* TabBar高度 */
  background-color: #F7FAFC;
}

.category-buttons {
  display: flex;
  justify-content: space-around;
  padding: 32rpx;
  background-color: #F7FAFC;
  gap: 60rpx;
}

.category-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx;
  background: #ffffff;
  border-radius: 16rpx;
  width: 200rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.category-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.category-text {
  font-size: 28rpx;
  color: #2d3748;
  font-weight: 500;
}
</style>